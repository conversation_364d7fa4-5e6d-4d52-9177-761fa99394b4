
import { useCallback, useEffect, useMemo } from 'react'
import type { Feature, FeatureInstance } from '@renderer/infrastructure/model'
import { CommandFeature } from '@renderer/infrastructure/model'
import { TabFeature } from '@renderer/infrastructure/model'
import { useFeatureStore } from '@renderer/store/feature-store'
import { useFeatureInstanceFactory } from '@renderer/infrastructure/services/application-service/feature/use-feature-instance-factory'
import { useLocation, useNavigate } from 'react-router-dom'

export function useFeatureManager() {
  const { newInstanceOf, newCommandOf } = useFeatureInstanceFactory()
  const navigate = useNavigate()
  const location = useLocation()
  const {
    fixedInstance,
    setFixedInstance,
    instances,
    setInstances,
    activeInstance,
    setActiveInstance: storeSetActiveInstance,
  } = useFeatureStore((state) => ({
    fixedInstance: state.fixedInstance,
    setFixedInstance: state.setFixedInstance,
    instances: state.instances,
    setInstances: state.setInstances,
    activeInstance: state.activeInstance,
    setActiveInstance: state.setActiveInstance,
  }))

  const isActiveInstance = useCallback(
    <TParam,>(featureInstance: FeatureInstance<TParam>) => {
      return activeInstance?.identifier === featureInstance.identifier
    },
    [activeInstance?.identifier],
  )

  const isActiveFeature = useCallback(
    <TParam,>(feature: TabFeature<TParam>) => {
      return activeInstance?.feature === feature
    },
    [activeInstance],
  )

  const setActiveInstance = useCallback(
    (featureInstance: FeatureInstance<unknown> | null) => {
      featureInstance && navigate(`/tasks`)
      storeSetActiveInstance(featureInstance)
    },
    [navigate, storeSetActiveInstance],
  )

  const openTabFeature = useCallback(
    <TParam,>(feature: TabFeature<TParam>, params?: TParam) => {
      console.log(feature, 1)
      navigate(`/tasks`)
      setInstances((prev) => {
        if (feature.isUnique) {
          const existing = prev.find((x) => x.feature.name === feature.name)
          if (existing) {
            setActiveInstance(existing)
            return prev
          }
        }

        const featureInstance = newInstanceOf<TParam>(feature, params)
        if (feature.isFixed) {
          setFixedInstance(featureInstance)
          setActiveInstance(featureInstance)
          return prev
        } else {
          const newInstance = featureInstance
          setActiveInstance(newInstance)
          return [...prev, newInstance]
        }
      })
    },
    [navigate, newInstanceOf, setActiveInstance, setFixedInstance, setInstances],
  )

  const openCommandFeature = useCallback(
    <TParam,>(feature: CommandFeature, params?: TParam) => {
      newCommandOf<TParam>(feature, params)
    },
    [newCommandOf],
  )

  const openFeature: {
    <TParam = undefined>(feature: Feature<TParam>): void
    <TParam>(feature: Feature<TParam>, params: TParam): void
  } = useCallback(
    <TParam,>(feature: Feature<TParam>, params?: TParam) => {
      console.log(feature)
      if (feature instanceof TabFeature) {
        openTabFeature<TParam>(feature, params)
      } else if (feature instanceof CommandFeature) {
        openCommandFeature<TParam>(feature, params)
      }
    },
    [openCommandFeature, openTabFeature],
  )

  const removeInstance = useCallback(
    <T,>(featureInstance: FeatureInstance<T>) => {
      setInstances((prev) => {
        const newFeatures = prev.filter((x) => x.identifier !== featureInstance.identifier)
        if (activeInstance?.identifier === featureInstance.identifier) {
          if (newFeatures.length > 0) {
            const currentIndex = prev.findIndex((x) => x.identifier === featureInstance.identifier)
            const neighborIndex = currentIndex > 0 ? currentIndex - 1 : 0
            setActiveInstance(newFeatures[neighborIndex])
          } else if (fixedInstance) {
            setActiveInstance(fixedInstance)
          } else {
            setActiveInstance(null)
            navigate('/')
          }
        }
        return newFeatures
      })
    },
    [activeInstance?.identifier, fixedInstance, navigate, setActiveInstance, setInstances],
  )

  // 清除所有实例
  const clearInstances = useCallback(() => {
    setInstances(() => [])
    if (fixedInstance) {
      setActiveInstance(fixedInstance)
    } else {
      setActiveInstance(null)
    }
  }, [fixedInstance, setActiveInstance, setInstances])

  useEffect(() => {
    if (location.pathname !== '/tasks') {
      setActiveInstance(null)
    }
  }, [location.pathname, setActiveInstance])

  return useMemo(
    () => ({
      fixedInstance,
      instances,
      activeInstance,
      openFeature,
      removeInstance,
      setActiveInstance,
      isActiveInstance,
      isActiveFeature,
      clearInstances,
    }),
    [
      fixedInstance,
      instances,
      activeInstance,
      openFeature,
      removeInstance,
      setActiveInstance,
      isActiveInstance,
      isActiveFeature,
      clearInstances,
    ],
  )
}

