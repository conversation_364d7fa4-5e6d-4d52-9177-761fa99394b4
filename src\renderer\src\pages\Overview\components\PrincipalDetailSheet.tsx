import { Sheet, She<PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>eader, SheetTitle } from '@renderer/shadcn-components/ui/sheet'
import { ScrollArea } from '@renderer/shadcn-components/ui/scroll-area'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@renderer/shadcn-components/ui/table'
import { useApiQuery } from '@renderer/hooks/useApiQuery'
import type {
  PrincipalDetailResponse,
  PrincipalAccountData,
} from '@renderer/infrastructure/types/principal'
import { datetimeService } from '@renderer/infrastructure/services'
import { LoadingContainer } from '@renderer/components/LoadingContainer'
import { ChevronUp, ChevronDown } from 'lucide-react'
import { sort } from '@renderer/utils/array'
import { useMemo, useState } from 'react'
import { Avatar, AvatarImage } from '@renderer/shadcn-components/ui/avatar'
import { getPlatformByName } from '@renderer/infrastructure/model'

type SortField =
  | 'platformAccountName'
  | 'platformName'
  | 'publishTotal'
  | 'fansTotal'
  | 'playTotal'
  | 'commentsTotal'
  | 'likesTotal'
  | 'favoritesTotal'
type SortOrder = 'asc' | 'desc' | null

interface SortState {
  field: SortField | null
  order: SortOrder
}

interface PrincipalDetailSheetProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  memberId: string | null
  memberName?: string
  memberAvatar?: string
}

export function PrincipalDetailSheet({
  open,
  onOpenChange,
  memberId,
  memberName,
  memberAvatar,
}: PrincipalDetailSheetProps) {
  const [sortState, setSortState] = useState<SortState>({ field: null, order: null })

  // 获取负责人详情数据
  const { data, isLoading, isError } = useApiQuery<PrincipalDetailResponse>(
    {
      url: `/platform-accounts/${memberId}/principal/overview`,
      method: 'GET',
    },
    ['principalDetail', memberId],
    {
      enabled: open && !!memberId,
    },
  )

  const formatCurrentTime = () => {
    const now = new Date()
    return datetimeService.formatToSeconds(now.getTime())
  }

  // 排序处理函数
  const handleSort = (field: SortField) => {
    setSortState((prev) => {
      if (prev.field === field) {
        // 同一字段：无排序 -> 升序 -> 降序 -> 无排序
        if (prev.order === null) return { field, order: 'asc' }
        if (prev.order === 'asc') return { field, order: 'desc' }
        return { field: null, order: null }
      } else {
        // 不同字段：直接设为升序
        return { field, order: 'asc' }
      }
    })
  }

  // 获取字段值的函数
  const getFieldValue = (item: PrincipalAccountData, field: SortField): number | string => {
    switch (field) {
      case 'platformAccountName':
        return item.platformAccountName || ''
      case 'platformName':
        return item.platformName || ''
      case 'publishTotal':
        return item.publishTotal || 0
      case 'fansTotal':
        return item.fansTotal || 0
      case 'playTotal':
        return item.playTotal || 0
      case 'commentsTotal':
        return item.commentsTotal || 0
      case 'likesTotal':
        return item.likesTotal || 0
      case 'favoritesTotal':
        return item.favoritesTotal || 0
      default:
        return 0
    }
  }

  // 排序后的数据
  const sortedData = useMemo(() => {
    if (!data?.data) return []

    let result = [...data.data]

    if (sortState.field && sortState.order) {
      result = result.sort(
        sort.by((item) => getFieldValue(item, sortState.field!), sortState.order === 'desc'),
      )
    }

    return result
  }, [data?.data, sortState])

  // 可排序表头组件
  const SortableTableHead = ({
    field,
    children,
  }: {
    field: SortField
    children: React.ReactNode
  }) => {
    const isActive = sortState.field === field
    const order = isActive ? sortState.order : null

    return (
      <TableHead
        className="left-0 z-10 w-auto min-w-fit cursor-pointer select-none whitespace-nowrap px-5 text-left hover:bg-gray-50"
        onClick={() => handleSort(field)}
      >
        <div className="flex items-center gap-1">
          <span>{children}</span>
          <div className="flex flex-col">
            <ChevronUp
              className={`h-3 w-3 ${isActive && order === 'asc' ? 'text-blue-600' : 'text-gray-400'}`}
            />
            <ChevronDown
              className={`-mt-1 h-3 w-3 ${isActive && order === 'desc' ? 'text-blue-600' : 'text-gray-400'}`}
            />
          </div>
        </div>
      </TableHead>
    )
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="flex w-[768px] !max-w-none flex-col bg-white">
        <SheetHeader className="shrink-0">
          <SheetTitle>查看详情</SheetTitle>
        </SheetHeader>

        {isLoading ? (
          <div className="flex flex-1 items-center justify-center">
            <LoadingContainer />
          </div>
        ) : isError ? (
          <div className="flex flex-1 items-center justify-center text-gray-500">
            加载失败，请重试
          </div>
        ) : data ? (
          <ScrollArea className="flex-1">
            <div className="space-y-6 p-1">
              {/* 负责人信息 */}
              <div className="flex h-[62px] items-center gap-3 rounded-lg bg-[#F9F9FA] px-4">
                <img src={memberAvatar} alt={memberName} className="h-8 w-8 rounded-full" />
                <h3 className="text-lg font-semibold">{memberName}</h3>
              </div>

              {/* 账号列表 */}
              <div className="space-y-4">
                {sortedData.length === 0 ? (
                  <div className="py-8 text-center text-gray-500">暂无负责的账号</div>
                ) : (
                  <div className="rounded-lg border">
                    <Table>
                      <TableHeader className="sticky top-[-16px] z-10 border-b-0 bg-secondary">
                        <TableRow className="border-none">
                          <TableHead className="left-0 z-10 w-auto min-w-fit whitespace-nowrap px-5 text-left">
                            账号
                          </TableHead>
                          <SortableTableHead field="publishTotal">发布</SortableTableHead>
                          <SortableTableHead field="fansTotal">粉丝</SortableTableHead>
                          <SortableTableHead field="playTotal">播放</SortableTableHead>
                          <SortableTableHead field="commentsTotal">评论</SortableTableHead>
                          <SortableTableHead field="likesTotal">点赞</SortableTableHead>
                          <SortableTableHead field="favoritesTotal">收藏</SortableTableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {sortedData.map((account) => (
                          <TableRow key={account.id} className="group !border-b border-[#E4E6EB]">
                            <TableCell className="w-auto min-w-fit whitespace-nowrap bg-white px-5 text-left font-medium transition-colors group-hover:bg-[#f8fafc]">
                              <div className="flex h-full w-[200px] items-center gap-2">
                                <div className="relative shrink-0">
                                  <Avatar className="h-9 w-9">
                                    <AvatarImage
                                      src={account.platformAvatar}
                                      alt={account.platformAccountName}
                                    />
                                  </Avatar>
                                  <img
                                    src={getPlatformByName(account.platformName).icon}
                                    alt=""
                                    className="absolute bottom-0 right-0 h-3.5 w-3.5 rounded-full"
                                  />
                                </div>
                                <div className="mr-2 flex w-0 grow flex-col">
                                  <span className="flex-1 truncate">
                                    {account.platformAccountName}
                                  </span>
                                  <div className="flex grow items-center text-xs">
                                    <div
                                      className={`mr-1 inline-block h-1.5 w-1.5 rounded-full ${account.status === 1 ? 'bg-green-600' : 'bg-destructive'}`}
                                    />
                                    <span
                                      className={`mr-2 text-xs ${account.status !== 1 ? 'text-destructive' : ''}`}
                                    >
                                      {account.status === 1 ? '正常' : '异常'}
                                    </span>
                                  </div>
                                  {account.updatedAt && (
                                    <span className="text-[12px] text-[#8A8A8A]">
                                      更新:
                                      {datetimeService.formatToSeconds(account.updatedAt)}
                                    </span>
                                  )}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="w-auto min-w-fit whitespace-nowrap bg-white px-5 text-left font-medium transition-colors group-hover:bg-[#f8fafc]">
                              {account.publishTotal?.toLocaleString()}
                            </TableCell>
                            <TableCell className="w-auto min-w-fit whitespace-nowrap bg-white px-5 text-left font-medium transition-colors group-hover:bg-[#f8fafc]">
                              {account.fansTotal.toLocaleString()}
                            </TableCell>
                            <TableCell className="w-auto min-w-fit whitespace-nowrap bg-white px-5 text-left font-medium transition-colors group-hover:bg-[#f8fafc]">
                              {account.playTotal.toLocaleString()}
                            </TableCell>
                            <TableCell className="w-auto min-w-fit whitespace-nowrap bg-white px-5 text-left font-medium transition-colors group-hover:bg-[#f8fafc]">
                              {account.commentsTotal.toLocaleString()}
                            </TableCell>
                            <TableCell className="w-auto min-w-fit whitespace-nowrap bg-white px-5 text-left font-medium transition-colors group-hover:bg-[#f8fafc]">
                              {account.likesTotal.toLocaleString()}
                            </TableCell>
                            <TableCell className="w-auto min-w-fit whitespace-nowrap bg-white px-5 text-left font-medium transition-colors group-hover:bg-[#f8fafc]">
                              {account.favoritesTotal.toLocaleString()}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </div>
            </div>
          </ScrollArea>
        ) : null}
      </SheetContent>
    </Sheet>
  )
}
