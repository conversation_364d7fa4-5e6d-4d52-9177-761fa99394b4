import { useEffect, useRef } from 'react'
import { useFeatureManager } from '@renderer/infrastructure/services'
import { cn } from '@renderer/lib/utils'
import CloseIcon from '@renderer/assets/common/cancel.svg?react'
import { IconMap } from '@renderer/pages/IndexPage/components/icon-map'
import OverflowTooltip from '@renderer/components/OverflowTooltip'
import { Button } from '@renderer/shadcn-components/ui/button'

export function DynamicFeatureTabItems({ expanded }: { expanded: boolean }) {
  const disabled = false //TODO 2025年4月7日，之前是根据是否新团队来禁用，如果后续没有这样的需求，可以去除disabled逻辑
  const {
    instances: openedFeatures,
    removeInstance,
    setActiveInstance,
    isActiveInstance,
  } = useFeatureManager()
  const containerRef = useRef<HTMLDivElement>(null)
  const activeInstanceRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (activeInstanceRef.current) {
      activeInstanceRef.current.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
    }
  }, [openedFeatures.length])

  return (
    <div ref={containerRef} className="flex shrink-0 flex-col gap-1">
      {openedFeatures.map((featureInstance) => {
        const active = isActiveInstance(featureInstance)
        return (
          <div key={featureInstance.identifier} className="flex justify-center">
            <div
              ref={active ? activeInstanceRef : null}
              className={cn(
                'group relative flex h-9 flex-1 cursor-pointer items-center overflow-hidden rounded-lg',
                expanded ? 'grow px-2' : 'h-[54px] w-14 px-0.5',
                active && !disabled ? 'bg-white' : 'hover:bg-[#FFFFFF80]',
                disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
              )}
              onClick={() => {
                if (!disabled) {
                  setActiveInstance(featureInstance)
                }
              }}
            >
              <div
                className={cn('flex flex-1 items-center gap-2 overflow-hidden', {
                  'flex-col justify-center gap-0.5': !expanded,
                })}
              >
                <span className="h-5 w-5 flex-shrink-0">
                  {active
                    ? IconMap[featureInstance.feature.name].active({
                        className: cn('h-5 w-5'),
                      })
                    : IconMap[featureInstance.feature.name].default({
                        className: cn('h-5 w-5'),
                      })}
                </span>

                <span
                  className={cn(
                    'flex-1 overflow-hidden text-sm font-medium',
                    { 'text-[#666666]': !active },
                    { 'max-w-full text-xs': !expanded },
                  )}
                >
                  <OverflowTooltip tooltip={featureInstance.displayName} delayDuration={700}>
                    {featureInstance.displayName}
                  </OverflowTooltip>
                </span>
              </div>

              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => {
                  e.stopPropagation()
                  // console.log(featureInstance.params) // 超级编导
                  removeInstance(featureInstance)
                }}
                className={cn(
                  'hidden h-5 w-5 p-0 text-foreground hover:bg-mainAccent group-hover:flex',
                  {
                    'absolute right-0.5 top-0.5 h-4 w-4': !expanded,
                  },
                )}
                disabled={disabled}
              >
                <CloseIcon className="h-3 w-3" />
              </Button>
            </div>
          </div>
        )
      })}
      <div className="electron-drag-region grow"></div>
    </div>
  )
}
