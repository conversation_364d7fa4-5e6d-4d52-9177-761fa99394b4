import { Button } from '@renderer/shadcn-components/ui/button'
import type { ShortcutFeatureGroup } from '@renderer/infrastructure/model'
import { unknowFeatureGroup } from '@renderer/infrastructure/model'
import { featureGroups } from '@renderer/infrastructure/model'
import { useFeatureManager } from '@renderer/infrastructure/services'

import { useState } from 'react'
import 发布视频Icon from '@renderer/assets/index-page/shortcut/发布视频.svg?react'
import 发布图文Icon from '@renderer/assets/index-page/shortcut/发布图文.svg?react'
import 发布文章Icon from '@renderer/assets/index-page/shortcut/发布文章.svg?react'
import 发布公众号Icon from '@renderer/assets/index-page/shortcut/发布公众号.svg?react'
import 添加账号Icon from '@renderer/assets/index-page/shortcut/添加账号.svg?react'
import TriggerIcon from '@renderer/assets/index-page/shortcut/trigger.svg?react'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@renderer/shadcn-components/ui/dropdown-menu'
import SauryTooltip from '@renderer/components/tooltip'
import { Plus } from 'lucide-react'
import { useGuiderStore } from '@renderer/store/guiderStore'
import { cn } from '@renderer/lib/utils'

const shortcutFeatureGroups: ShortcutFeatureGroup[] = [
  featureGroups.发布视频,
  featureGroups.发布图文,
  featureGroups.发布文章,
  featureGroups.发布公众号,
  featureGroups.添加账号,
] as const

const iconMap = {
  [featureGroups.发布视频.name]: <发布视频Icon />,
  [featureGroups.发布图文.name]: <发布图文Icon />,
  [featureGroups.发布文章.name]: <发布文章Icon />,
  [featureGroups.发布公众号.name]: <发布公众号Icon />,
  [featureGroups.添加账号.name]: <添加账号Icon />,
}

export class FavoriteGroupOption {
  constructor(
    public identifier: string,
    public name: string,
  ) {}
}

export function ShortcutContextMenu({ size = 'small' }: { size?: 'small' | 'large' }) {
  const { isNewTeam } = useGuiderStore((store) => ({
    isNewTeam: store.isNewTeam,
  }))

  const { openFeature } = useFeatureManager()

  const [open, setOpen] = useState(false)

  const shortcuts = shortcutFeatureGroups

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild disabled={isNewTeam}>
          <div className={cn('flex items-center', isNewTeam && 'cursor-not-allowed opacity-50')}>
            <SauryTooltip tooltip="快捷入口">
              {size === 'small' ? (
                <Button
                  size="small_icon"
                  variant="ghost"
                  className="hover:bg-mainAccent"
                  disabled={isNewTeam}
                >
                  <TriggerIcon />
                </Button>
              ) : (
                <Button
                  size="icon"
                  variant="ghost"
                  className="h-8 w-8 rounded-full bg-background p-0 text-textSecondary hover:bg-mainAccent"
                  disabled={isNewTeam}
                >
                  <Plus className="h-5 w-5" />
                </Button>
              )}
            </SauryTooltip>
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className="electron-no-drag space-y-1"
          align="start"
          sideOffset={8}
          side="right"
        >
          {shortcuts.map(
            (shortcut) =>
              shortcut !== unknowFeatureGroup && (
                <DropdownMenuItem
                  key={shortcut.name}
                  className="relative flex items-center"
                  onClick={() => openFeature(shortcut.features[0])}
                >
                  <div className="shrink-0">{iconMap[shortcut.name]}</div>
                  <div className="truncate text-sm font-medium">{shortcut.name}</div>
                </DropdownMenuItem>
              ),
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  )
}
